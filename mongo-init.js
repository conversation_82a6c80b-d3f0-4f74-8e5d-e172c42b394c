// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

// Switch to the game database
db = db.getSiblingDB('express-game');

// Create a user for the application
db.createUser({
  user: 'gameuser',
  pwd: 'gamepassword',
  roles: [
    {
      role: 'readWrite',
      db: 'express-game'
    }
  ]
});

// Create collections with validation
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'password'],
      properties: {
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
          description: 'must be a valid email address'
        },
        password: {
          bsonType: 'string',
          minLength: 6,
          description: 'must be a string with at least 6 characters'
        },
        isActive: {
          bsonType: 'bool',
          description: 'must be a boolean'
        },
        profile: {
          bsonType: 'object',
          properties: {
            firstName: {
              bsonType: 'string',
              maxLength: 50
            },
            lastName: {
              bsonType: 'string',
              maxLength: 50
            },
            avatar: {
              bsonType: 'string'
            },
            bio: {
              bsonType: 'string',
              maxLength: 500
            }
          }
        }
      }
    }
  }
});

db.createCollection('characters', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['name', 'userId'],
      properties: {
        name: {
          bsonType: 'string',
          minLength: 2,
          maxLength: 20,
          description: 'must be a string between 2-20 characters'
        },
        userId: {
          bsonType: 'objectId',
          description: 'must be a valid ObjectId'
        },
        experience: {
          bsonType: 'int',
          minimum: 0,
          maximum: 999999999
        },
        level: {
          bsonType: 'int',
          minimum: 1,
          maximum: 100
        },
        attack: {
          bsonType: 'int',
          minimum: 1,
          maximum: 9999
        },
        defense: {
          bsonType: 'int',
          minimum: 1,
          maximum: 9999
        },
        criticalChance: {
          bsonType: 'int',
          minimum: 0,
          maximum: 100
        },
        criticalDamage: {
          bsonType: 'int',
          minimum: 100,
          maximum: 1000
        },
        criticalResistance: {
          bsonType: 'int',
          minimum: 0,
          maximum: 100
        },
        isActive: {
          bsonType: 'bool'
        }
      }
    }
  }
});

// Create indexes for better performance
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ isActive: 1 });
db.users.createIndex({ createdAt: -1 });

db.characters.createIndex({ userId: 1, isActive: 1 });
db.characters.createIndex({ name: 1, userId: 1 });
db.characters.createIndex({ level: -1 });
db.characters.createIndex({ experience: -1 });

print('Database initialization completed successfully!');
print('Created collections: users, characters');
print('Created indexes for optimal performance');
print('Created application user: gameuser');
