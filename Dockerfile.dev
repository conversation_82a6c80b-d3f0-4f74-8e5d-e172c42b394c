# Development Dockerfile with hot reload
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install nodemon globally for development
RUN npm install -g nodemon ts-node

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Copy source code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership of the app directory
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port and debug port
EXPOSE 3000 9229

# Start the application in development mode with debugging
CMD ["npm", "run", "start:dev"]
