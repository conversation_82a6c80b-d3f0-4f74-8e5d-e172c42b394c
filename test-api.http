### Health Check
GET http://localhost:3000/health

### Create User (sẽ tự động tạo character "<PERSON>ư<PERSON><PERSON> chơi")
POST http://localhost:3000/api/users
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "profile": {
    "firstName": "Test",
    "lastName": "User",
    "bio": "This is a test user"
  }
}

### Get All Users
GET http://localhost:3000/api/users

### Get User by ID (replace with actual ID)
GET http://localhost:3000/api/users/USER_ID_HERE

### Update User (replace with actual ID)
PUT http://localhost:3000/api/users/USER_ID_HERE
Content-Type: application/json

{
  "profile": {
    "firstName": "Updated",
    "lastName": "Name",
    "bio": "Updated bio"
  }
}

### Create Character (replace USER_ID_HERE with actual user ID)
POST http://localhost:3000/api/characters
Content-Type: application/json

{
  "name": "TestWarrior",
  "userId": "USER_ID_HERE"
}

### Get All Characters
GET http://localhost:3000/api/characters

### Get Character by ID (replace with actual ID)
GET http://localhost:3000/api/characters/CHARACTER_ID_HERE

### Update Character (replace with actual ID)
PUT http://localhost:3000/api/characters/CHARACTER_ID_HERE
Content-Type: application/json

{
  "attack": 15,
  "defense": 8
}

### Level Up Character (replace with actual ID)
POST http://localhost:3000/api/characters/CHARACTER_ID_HERE/levelup

### Get User's Characters (replace with actual user ID)
GET http://localhost:3000/api/users/USER_ID_HERE/characters

### Get Characters by User ID (replace with actual user ID)
GET http://localhost:3000/api/characters/user/USER_ID_HERE

### Delete Character (replace with actual ID)
DELETE http://localhost:3000/api/characters/CHARACTER_ID_HERE

### Delete User (replace with actual ID)
DELETE http://localhost:3000/api/users/USER_ID_HERE
