import { IUser } from "../models/entities/User";
import { UserRepository } from "../repositories/userRepository";
import { CharacterRepository } from "../repositories/characterRepository";
import { createError } from "../middleware/errorHandler";
import bcrypt from "bcryptjs";

export class UserService {
  private userRepository: UserRepository;
  private characterRepository: CharacterRepository;

  constructor() {
    this.userRepository = new UserRepository();
    this.characterRepository = new CharacterRepository();
  }

  async createUser(userData: Partial<IUser>): Promise<IUser> {
    try {
      // Check if email already exists
      const existingUser = await this.userRepository.findByEmail(userData.email!);
      if (existingUser) {
        throw createError("Email already exists", 409);
      }

      // Hash password
      if (userData.password) {
        const saltRounds = 12;
        userData.password = await bcrypt.hash(userData.password, saltRounds);
      }

      // Create user
      const user = await this.userRepository.create(userData);

      // Automatically create default character "Người chơi"
      await this.characterRepository.create({
        name: "Người chơi",
        userId: user._id
      });

      return user;
    } catch (error) {
      if (error instanceof Error && (error as any).statusCode) {
        throw error;
      }
      throw createError("Failed to create user", 500);
    }
  }

  async getAllUsers(): Promise<IUser[]> {
    try {
      return await this.userRepository.findAll();
    } catch (error) {
      throw createError("Failed to fetch users", 500);
    }
  }

  async getUserById(id: string): Promise<IUser> {
    try {
      const user = await this.userRepository.findById(id);

      if (!user) {
        throw createError("User not found", 404);
      }

      return user;
    } catch (error) {
      if (error instanceof Error && (error as any).statusCode) {
        throw error;
      }
      throw createError("Failed to fetch user", 500);
    }
  }

  async updateUser(id: string, updateData: Partial<IUser>): Promise<IUser> {
    try {
      // Check if user exists
      const existingUser = await this.userRepository.findById(id);
      if (!existingUser) {
        throw createError("User not found", 404);
      }

      // Check for duplicate email if being updated
      if (updateData.email) {
        const emailExists = await this.userRepository.checkEmailExists(updateData.email, id);
        if (emailExists) {
          throw createError("Email already exists", 409);
        }
      }

      const user = await this.userRepository.update(id, updateData);

      if (!user) {
        throw createError("User not found", 404);
      }

      return user;
    } catch (error) {
      if (error instanceof Error && (error as any).statusCode) {
        throw error;
      }
      throw createError("Failed to update user", 500);
    }
  }

  async deleteUser(id: string): Promise<void> {
    try {
      const user = await this.userRepository.softDelete(id);

      if (!user) {
        throw createError("User not found", 404);
      }

      // Also deactivate user's characters
      await this.characterRepository.softDeleteByUserId(id);
    } catch (error) {
      if (error instanceof Error && (error as any).statusCode) {
        throw error;
      }
      throw createError("Failed to delete user", 500);
    }
  }

  async getUserCharacters(userId: string): Promise<any[]> {
    try {
      // First check if user exists
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw createError("User not found", 404);
      }

      const characters = await this.characterRepository.findByUserId(userId);

      return characters;
    } catch (error) {
      if (error instanceof Error && (error as any).statusCode) {
        throw error;
      }
      throw createError("Failed to fetch user characters", 500);
    }
  }
}
