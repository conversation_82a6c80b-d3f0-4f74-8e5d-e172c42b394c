import { SignInExecute, SignUpExecute } from "../models/executes/auth.executes";
import { JwtService } from "./jwtService";
import { UserService } from "./userService";

export class AuthService {
  private usersService: UserService;
   private jwtService: JwtService;

  constructor(userService: UserService, jwtService: JwtService) {
    this.usersService = userService;
    this.jwtService = jwtService;
  }
  async signUpUser(authData: Partial<SignUpExecute>): Promise<SignUpExecute> {
    try {
      const exitingUser = await this.usersService.find;
    } catch (error) {}
  }

  async singInUser(authData: Partial<SignInExecute>): Promise<SignInExecute> {
    try {
      const;
    } catch (error) {}
  }
}
