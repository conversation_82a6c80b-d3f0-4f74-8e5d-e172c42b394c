import { Character, <PERSON><PERSON><PERSON>cter } from '../models/entities/Character';
import { Types } from 'mongoose';

export class CharacterRepository {
  async create(characterData: Partial<ICharacter>): Promise<ICharacter> {
    const character = new Character(characterData);
    return await character.save();
  }

  async findAll(filter: any = {}): Promise<ICharacter[]> {
    return await Character.find({ isActive: true, ...filter })
      .populate('userId', 'email profile')
      .sort({ level: -1, experience: -1 });
  }

  async findById(id: string): Promise<ICharacter | null> {
    return await Character.findOne({ _id: id, isActive: true })
      .populate('userId', 'email profile');
  }

  async findByUserId(userId: string): Promise<ICharacter[]> {
    return await Character.find({ userId, isActive: true })
      .populate('userId', 'email profile')
      .sort({ level: -1, experience: -1 });
  }

  async findByName(name: string, userId: string): Promise<ICharacter | null> {
    return await Character.findOne({
      name,
      userId,
      isActive: true
    });
  }

  async update(id: string, updateData: Partial<ICharacter>): Promise<ICharacter | null> {
    return await Character.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('userId', 'email profile');
  }

  async softDelete(id: string): Promise<ICharacter | null> {
    return await Character.findByIdAndUpdate(
      id,
      { isActive: false },
      { new: true }
    );
  }

  async softDeleteByUserId(userId: string): Promise<void> {
    await Character.updateMany(
      { userId },
      { isActive: false }
    );
  }

  async levelUp(id: string, levelIncrease: number = 1): Promise<ICharacter | null> {
    const character = await Character.findOne({ _id: id, isActive: true });
    if (!character) return null;

    const newLevel = character.level + levelIncrease;
    const statIncrease = Math.floor(newLevel / 10) + 1;

    return await Character.findByIdAndUpdate(
      id,
      {
        $inc: {
          level: levelIncrease,
          attack: statIncrease,
          defense: Math.floor(statIncrease / 2) || 1,
          criticalChance: newLevel % 10 === 0 ? 1 : 0
        }
      },
      { new: true, runValidators: true }
    ).populate('userId', 'email profile');
  }

  async count(filter: any = {}): Promise<number> {
    return await Character.countDocuments({ isActive: true, ...filter });
  }

  async getAverageLevel(): Promise<number> {
    const result = await Character.aggregate([
      { $match: { isActive: true } },
      { $group: { _id: null, averageLevel: { $avg: '$level' } } }
    ]);
    
    return result.length > 0 ? Math.round(result[0].averageLevel) : 0;
  }

  async findWithPagination(
    filter: any = {},
    page: number = 1,
    limit: number = 10
  ): Promise<{ characters: ICharacter[]; total: number; totalPages: number }> {
    const skip = (page - 1) * limit;
    
    const [characters, total] = await Promise.all([
      Character.find({ isActive: true, ...filter })
        .populate('userId', 'email profile')
        .sort({ level: -1, experience: -1 })
        .skip(skip)
        .limit(limit),
      this.count(filter)
    ]);

    return {
      characters,
      total,
      totalPages: Math.ceil(total / limit)
    };
  }

  async getTopCharacters(limit: number = 10): Promise<ICharacter[]> {
    return await Character.find({ isActive: true })
      .populate('userId', 'email profile')
      .sort({ level: -1, experience: -1 })
      .limit(limit);
  }
}
