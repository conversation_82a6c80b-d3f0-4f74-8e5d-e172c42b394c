import { ICharacter } from '../models/entities/Character';
import { CharacterRepository } from '../repositories/characterRepository';
import { UserRepository } from '../repositories/userRepository';
import { createError } from '../middleware/errorHandler';

export class CharacterService {
  private characterRepository: CharacterRepository;
  private userRepository: UserRepository;

  constructor() {
    this.characterRepository = new CharacterRepository();
    this.userRepository = new UserRepository();
  }

  async createCharacter(characterData: Partial<ICharacter>): Promise<ICharacter> {
    try {
      const userIdString = characterData.userId?.toString();
      if (!userIdString) {
        throw createError('User ID is required', 400);
      }

      // Check if user exists
      const user = await this.userRepository.findById(userIdString);
      if (!user) {
        throw createError('User not found', 404);
      }

      // Check if character name already exists for this user
      const existingCharacter = await this.characterRepository.findByName(
        characterData.name!,
        userIdString
      );

      if (existingCharacter) {
        throw createError('Character name already exists for this user', 409);
      }

      const character = await this.characterRepository.create(characterData);

      return character;
    } catch (error) {
      if (error instanceof Error && (error as any).statusCode) {
        throw error;
      }
      throw createError('Failed to create character', 500);
    }
  }

  async getAllCharacters(): Promise<ICharacter[]> {
    try {
      return await this.characterRepository.findAll();
    } catch (error) {
      throw createError('Failed to fetch characters', 500);
    }
  }

  async getCharacterById(id: string): Promise<ICharacter> {
    try {
      const character = await this.characterRepository.findById(id);
      
      if (!character) {
        throw createError('Character not found', 404);
      }
      
      return character;
    } catch (error) {
      if (error instanceof Error && (error as any).statusCode) {
        throw error;
      }
      throw createError('Failed to fetch character', 500);
    }
  }

  async updateCharacter(id: string, updateData: Partial<ICharacter>): Promise<ICharacter> {
    try {
      // Check if character exists
      const existingCharacter = await this.characterRepository.findById(id);
      if (!existingCharacter) {
        throw createError('Character not found', 404);
      }

      // Check for duplicate name if being updated
      if (updateData.name) {
        const duplicateCharacter = await this.characterRepository.findByName(
          updateData.name,
          existingCharacter.userId.toString()
        );

        if (duplicateCharacter && duplicateCharacter._id.toString() !== id) {
          throw createError('Character name already exists for this user', 409);
        }
      }

      const character = await this.characterRepository.update(id, updateData);

      if (!character) {
        throw createError('Character not found', 404);
      }

      return character;
    } catch (error) {
      if (error instanceof Error && (error as any).statusCode) {
        throw error;
      }
      throw createError('Failed to update character', 500);
    }
  }

  async deleteCharacter(id: string): Promise<void> {
    try {
      const character = await this.characterRepository.softDelete(id);

      if (!character) {
        throw createError('Character not found', 404);
      }
    } catch (error) {
      if (error instanceof Error && (error as any).statusCode) {
        throw error;
      }
      throw createError('Failed to delete character', 500);
    }
  }

  async getCharactersByUserId(userId: string): Promise<ICharacter[]> {
    try {
      // Check if user exists
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw createError('User not found', 404);
      }

      const characters = await this.characterRepository.findByUserId(userId);

      return characters;
    } catch (error) {
      if (error instanceof Error && (error as any).statusCode) {
        throw error;
      }
      throw createError('Failed to fetch characters', 500);
    }
  }

  async levelUpCharacter(id: string): Promise<ICharacter> {
    try {
      const character = await this.characterRepository.findById(id);
      if (!character) {
        throw createError('Character not found', 404);
      }

      if (character.level >= 100) {
        throw createError('Character is already at maximum level', 400);
      }

      const updatedCharacter = await this.characterRepository.levelUp(id);

      if (!updatedCharacter) {
        throw createError('Character not found', 404);
      }

      return updatedCharacter;
    } catch (error) {
      if (error instanceof Error && (error as any).statusCode) {
        throw error;
      }
      throw createError('Failed to level up character', 500);
    }
  }

  async getCharacterStats(): Promise<{ total: number; averageLevel: number }> {
    try {
      const [total, averageLevel] = await Promise.all([
        this.characterRepository.count(),
        this.characterRepository.getAverageLevel()
      ]);

      return { total, averageLevel };
    } catch (error) {
      throw createError('Failed to fetch character stats', 500);
    }
  }

  async getTopCharacters(limit: number = 10): Promise<ICharacter[]> {
    try {
      return await this.characterRepository.getTopCharacters(limit);
    } catch (error) {
      throw createError('Failed to fetch top characters', 500);
    }
  }
}
