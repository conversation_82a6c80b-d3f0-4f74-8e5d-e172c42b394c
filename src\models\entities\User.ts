import mongoose, { Document, Schema } from 'mongoose';

export interface IProfile {
  firstName?: string;
  lastName?: string;
  avatar?: string;
  bio?: string;
}

export interface IUser extends Document {
  email: string;
  password: string;
  isActive: boolean;
  profile?: IProfile;
  createdAt: Date;
  updatedAt: Date;
}

const ProfileSchema = new Schema<IProfile>({
  firstName: {
    type: String,
    trim: true,
    maxlength: 50
  },
  lastName: {
    type: String,
    trim: true,
    maxlength: 50
  },
  avatar: {
    type: String
  },
  bio: {
    type: String,
    maxlength: 500
  }
}, { _id: false });

const UserSchema = new Schema<IUser>({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    index: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  profile: {
    type: ProfileSchema,
    default: {}
  }
}, {
  timestamps: true,
  collection: 'users'
});

// Indexes for better performance
UserSchema.index({ email: 1, isActive: 1 });

// Transform output to remove password and __v
UserSchema.set('toJSON', {
  transform: function(doc, ret) {
    delete ret.password;
    delete ret.__v;
    return ret;
  }
});

export const User = mongoose.model<IUser>('User', UserSchema);
