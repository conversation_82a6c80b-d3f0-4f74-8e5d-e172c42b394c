# Express MongoDB Game API

A RESTful API built with Express.js, TypeScript, and MongoDB for managing users and game characters.

## Features

- **User Management**: Create, read, update, and delete users
- **Character Management**: Create and manage game characters with stats
- **Data Validation**: Comprehensive input validation using Joi
- **Error Handling**: Centralized error handling with custom error types
- **Database**: MongoDB with Mongoose ODM
- **TypeScript**: Full TypeScript support for type safety
- **Security**: Helmet for security headers, CORS support
- **Logging**: Morgan for HTTP request logging

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: MongoDB
- **ODM**: Mongoose
- **Validation**: Joi
- **Security**: Helmet, CORS
- **Logging**: Morgan
- **Development**: ts-node-dev, nodemon

## Project Structure

```
src/
├── app.ts                 # Main application file
├── config/
│   └── database.ts        # Database configuration
├── controllers/           # Route controllers
│   ├── userController.ts
│   └── characterController.ts
├── middleware/            # Custom middleware
│   ├── errorHandler.ts
│   ├── notFoundHandler.ts
│   └── validation.ts
├── models/               # Mongoose models
│   ├── User.ts
│   └── Character.ts
├── routes/               # Route definitions
│   ├── userRoutes.ts
│   └── characterRoutes.ts
├── services/             # Business logic
│   ├── userService.ts
│   └── characterService.ts
└── validators/           # Joi validation schemas
    ├── userValidators.ts
    └── characterValidators.ts
```

## Installation

### Option 1: Using Docker (Recommended)

1. Clone the repository
2. Make sure Docker and Docker Compose are installed on your system
3. Run the application:
   ```bash
   # For development with hot reload
   docker-compose -f docker-compose.dev.yml up --build

   # For production
   docker-compose up --build
   ```

4. The application will be available at:
   - API: http://localhost:3000
   - MongoDB Admin: http://localhost:8081 (admin/admin123)
   - Health Check: http://localhost:3000/health

### Option 2: Local Development

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file in the root directory with the following variables:
   ```env
   NODE_ENV=development
   PORT=3000
   MONGODB_URI=mongodb://localhost:27017/express-game
   ```

4. Make sure MongoDB is running on your system
5. Start the development server:
   ```bash
   npm run start:dev
   ```

## Scripts

- `npm run start:dev` - Start development server with hot reload
- `npm run build` - Build the TypeScript code
- `npm run start` - Start production server
- `npm run start:prod` - Start production server

## Docker Commands

### Development
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up --build

# Stop development environment
docker-compose -f docker-compose.dev.yml down

# View logs
docker-compose -f docker-compose.dev.yml logs -f api-dev

# Rebuild only API container
docker-compose -f docker-compose.dev.yml up --build api-dev
```

### Production
```bash
# Start production environment
docker-compose up --build -d

# Stop production environment
docker-compose down

# View logs
docker-compose logs -f api

# Scale API instances
docker-compose up --scale api=3
```

### Database Management
```bash
# Access MongoDB shell
docker exec -it game-mongodb-dev mongosh -u admin -p password123

# Backup database
docker exec game-mongodb-dev mongodump --uri="*****************************************************************************" --out=/backup

# Restore database
docker exec game-mongodb-dev mongorestore --uri="*****************************************************************************" /backup/express-game-dev
```

## API Endpoints

### Users

- `POST /api/users` - Create a new user
- `GET /api/users` - Get all users
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user (soft delete)
- `GET /api/users/:id/characters` - Get user's characters

### Characters

- `POST /api/characters` - Create a new character
- `GET /api/characters` - Get all characters
- `GET /api/characters/:id` - Get character by ID
- `PUT /api/characters/:id` - Update character
- `DELETE /api/characters/:id` - Delete character (soft delete)
- `GET /api/characters/user/:userId` - Get characters by user ID
- `POST /api/characters/:id/levelup` - Level up character

### Health Check

- `GET /health` - Server health check

## Data Models

### User
```typescript
{
  email: string;         // Valid email, unique
  password: string;      // Minimum 6 characters (hashed)
  isActive: boolean;     // Default: true
  profile?: {
    firstName?: string;  // Max 50 characters
    lastName?: string;   // Max 50 characters
    avatar?: string;     // URL
    bio?: string;        // Max 500 characters
  };
  createdAt: Date;
  updatedAt: Date;
}
```

### Character
```typescript
{
  name: string;              // 2-20 characters
  userId: ObjectId;          // Reference to User
  experience: number;        // 0-999,999,999
  level: number;             // 1-100
  attack: number;            // 1-9999
  defense: number;           // 1-9999
  criticalChance: number;    // 0-100
  criticalDamage: number;    // 100-1000
  criticalResistance: number; // 0-100
  isActive: boolean;         // Default: true
  createdAt: Date;
  updatedAt: Date;
}
```

## Error Handling

The API uses a centralized error handling system that returns consistent error responses:

```json
{
  "success": false,
  "error": {
    "message": "Error description"
  }
}
```

## Development

To start development:

1. Ensure MongoDB is running
2. Run `npm run start:dev`
3. The server will start on `http://localhost:3000`
4. Check health at `http://localhost:3000/health`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request
