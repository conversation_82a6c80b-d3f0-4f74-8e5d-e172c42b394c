version: '3.8'

services:
  # MongoDB Database for Development
  mongodb:
    image: mongo:7.0
    container_name: game-mongodb-dev
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: express-game-dev
    volumes:
      - mongodb_dev_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - game-dev-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Development API with hot reload
  api-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: game-api-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
      - "9229:9229"  # Debug port
    environment:
      NODE_ENV: development
      PORT: 3000
      MONGODB_URI: ***************************************************************************
      JWT_SECRET: dev-secret-key
      JWT_EXPIRE: 7d
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - game-dev-network
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    command: npm run start:dev

  # MongoDB Admin Interface
  mongo-express:
    image: mongo-express:1.0.0
    container_name: game-mongo-express-dev
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    depends_on:
      mongodb:
        condition: service_healthy
    networks:
      - game-dev-network

volumes:
  mongodb_dev_data:
    driver: local

networks:
  game-dev-network:
    driver: bridge
