import { User, IUser } from '../models/entities/User';
import { Types } from 'mongoose';

export class UserRepository {
  async create(userData: Partial<IUser>): Promise<IUser> {
    const user = new User(userData);
    return await user.save();
  }

  async findAll(filter: any = {}): Promise<IUser[]> {
    return await User.find({ isActive: true, ...filter })
      .select('-password')
      .sort({ createdAt: -1 });
  }

  async findById(id: string): Promise<IUser | null> {
    return await User.findOne({ _id: id, isActive: true })
      .select('-password') as IUser | null;
  }

  async findByEmail(email: string): Promise<IUser | null> {
    return await User.findOne({ email, isActive: true }) as IUser | null;
  }

  async findByEmailWithPassword(email: string): Promise<IUser | null> {
    return await User.findOne({ email, isActive: true }) as IUser | null;
  }

  async update(id: string, updateData: Partial<IUser>): Promise<IUser | null> {
    return await User.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select('-password') as IUser | null;
  }

  async softDelete(id: string): Promise<IUser | null> {
    return await User.findByIdAndUpdate(
      id,
      { isActive: false },
      { new: true }
    );
  }

  async checkEmailExists(email: string, excludeId?: string): Promise<boolean> {
    const query: any = { email };
    if (excludeId) {
      query._id = { $ne: excludeId };
    }
    const user = await User.findOne(query);
    return !!user;
  }

  async count(filter: any = {}): Promise<number> {
    return await User.countDocuments({ isActive: true, ...filter });
  }

  async findWithPagination(
    filter: any = {},
    page: number = 1,
    limit: number = 10
  ): Promise<{ users: IUser[]; total: number; totalPages: number }> {
    const skip = (page - 1) * limit;
    
    const [users, total] = await Promise.all([
      User.find({ isActive: true, ...filter })
        .select('-password')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      this.count(filter)
    ]);

    return {
      users,
      total,
      totalPages: Math.ceil(total / limit)
    };
  }
}
